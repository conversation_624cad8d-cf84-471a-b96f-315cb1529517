@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card-background: #ffffff;
  --border-color: #e5e7eb;
}

:root.dark {
  --background: #171717;
  --foreground: #ededed;
  --card-background: #1f1f1f;
  --border-color: #374151;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode styles for Ant Design components */
.dark .ant-layout {
  background: var(--background) !important;
}

.dark .ant-layout-header {
  background: var(--card-background) !important;
  border-bottom: 1px solid var(--border-color);
}

.dark .ant-layout-content {
  background: var(--background) !important;
}

.dark .ant-layout-footer {
  background: var(--card-background) !important;
  color: var(--foreground) !important;
  border-top: 1px solid var(--border-color);
}

.dark .ant-card {
  background: var(--card-background) !important;
  border-color: var(--border-color) !important;
}

.dark .ant-card-head {
  background: var(--card-background) !important;
  border-bottom-color: var(--border-color) !important;
}

.dark .ant-card-head-title {
  color: var(--foreground) !important;
}

.dark .ant-typography {
  color: var(--foreground) !important;
}

.dark .ant-btn-text {
  color: var(--foreground) !important;
}

.dark .ant-btn-text:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.dark .ant-statistic {
  color: var(--foreground) !important;
}

.dark .ant-statistic-title {
  color: var(--foreground) !important;
}

.dark .ant-statistic-content {
  color: var(--foreground) !important;
}

.dark .ant-list-item {
  border-bottom-color: var(--border-color) !important;
}

.dark .ant-list-item-meta-title > a {
  color: var(--foreground) !important;
}

.dark .ant-list-item-meta-description {
  color: rgba(255, 255, 255, 0.65) !important;
}

.dark .ant-input {
  background: var(--card-background) !important;
  border-color: var(--border-color) !important;
  color: var(--foreground) !important;
}

.dark .ant-select-selector {
  background: var(--card-background) !important;
  border-color: var(--border-color) !important;
  color: var(--foreground) !important;
}

.dark .ant-divider-horizontal.ant-divider-with-text {
  color: var(--foreground) !important;
}

/* Header button hover effects */
.dark .ant-layout-header .ant-btn-text:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: var(--foreground) !important;
}

/* Badge icon hover effects */
.anticon:hover {
  color: #1890ff !important;
  transform: scale(1.1);
  transition: all 0.2s ease;
}

.dark .anticon:hover {
  color: #40a9ff !important;
}

/* Badge styles */
.ant-badge {
  cursor: pointer;
}

.ant-badge:hover .anticon {
  color: #1890ff !important;
}

.dark .ant-badge:hover .anticon {
  color: #40a9ff !important;
}
