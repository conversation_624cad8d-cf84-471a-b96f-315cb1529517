{"name": "health-care-wed-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "antd": "^5.26.3", "chart.js": "2.9.3", "next": "15.3.4", "react": "18", "react-countup": "^6.5.3", "react-dom": "18", "react-infinite-scroll-component": "^6.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}