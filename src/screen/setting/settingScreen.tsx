"use client";

import React from 'react';
import { Card, Switch, Typography, Space } from 'antd';
import { MoonOutlined, SunOutlined } from '@ant-design/icons';
import { useTheme } from '../../hooks/useTheme';

const { Title, Text } = Typography;

export const SettingScreen = () => {
  const { isDarkMode, toggleDarkMode } = useTheme();

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>Settings</Title>

      <Card title="Appearance" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              {isDarkMode ? <MoonOutlined /> : <SunOutlined />}
              <div>
                <Text strong>Dark Mode</Text>
                <br />
                <Text type="secondary">
                  {isDarkMode ? 'Switch to light theme' : 'Switch to dark theme'}
                </Text>
              </div>
            </Space>
            <Switch
              checked={isDarkMode}
              onChange={toggleDarkMode}
              checkedChildren={<MoonOutlined />}
              unCheckedChildren={<SunOutlined />}
            />
          </div>
        </Space>
      </Card>

      <Card title="System Information">
        <Space direction="vertical">
          <Text>
            <Text strong>Application:</Text> Magnet Software House Admin
          </Text>
          <Text>
            <Text strong>Version:</Text> 1.0.0
          </Text>
          <Text>
            <Text strong>Theme:</Text> {isDarkMode ? 'Dark' : 'Light'}
          </Text>
        </Space>
      </Card>
    </div>
  );
};
