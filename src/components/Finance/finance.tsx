"use client";

import React, { useEffect, useState } from "react";
import { Select } from "antd";
import { AutoComplete, Input } from "antd";
import { statisticClasses } from "../CustomStyle";
import { DatePicker, Space } from "antd";
import type { DatePickerProps } from "antd";
import CardLine<PERSON>hart from "../chart/chartScreen";
import { useTheme } from "../../hooks/useTheme";

export const Finance = () => {
  const defaultIndex = 0;
  const currentDate = new Date();
  const [selectedDate, setSelectedDate] = useState(currentDate);

  const Selectproduct = [
    { id: 1, productName: "Health Care" },
    { id: 2, productName: "ChangYai" },
  ];

  const onChange: DatePickerProps["onChange"] = (date, dateString) => {
    console.log(date, dateString);
    if (date) {
      setSelectedDate(date.toDate());
    }
  };

  return (
    <>
      <div style={statisticClasses.continueHeader}>
        Finance and Accounting
        <div style={{ display: "flex", alignItems: "flex-end", gap: "16px" }}>
          <DatePicker
            onChange={onChange}
            picker="month"
            placeholder={`${new Date().getFullYear()}-${String(
              new Date().getMonth() + 1
            ).padStart(2, "0")}`}
          />
          <Select
            showSearch
            style={{ width: 200 }}
            placeholder="Search to Select"
            optionFilterProp="label"
            defaultValue={Selectproduct[defaultIndex]?.id}
            filterOption={(input, option) =>
              (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
              (optionA?.label ?? "")
                .toLowerCase()
                .localeCompare((optionB?.label ?? "").toLowerCase())
            }
            options={Selectproduct.map((item) => ({
              value: item.id,
              label: item.productName,
            }))}
          />
        </div>
      </div>
      <CardLineChart />
    </>
  );
};
