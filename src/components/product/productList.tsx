import { Button } from "antd";
import Image from "next/image";
import { productClasses } from "../CustomStyle";
import React, { useEffect, useState } from "react";
import { Divider, List, Skeleton, Tag } from "antd";
import CangYai from "../../assets/images/cangYai.png";
import { PlusCircleOutlined } from "@ant-design/icons";
import HealthCare from "../../assets/images/healthCare.png";
import HCpartners from "../../assets/images/HCpartners.png";
import CYpartners from "../../assets/images/CYpartners.png";
import InfiniteScroll from "react-infinite-scroll-component";

export const ProductList = () => {
  interface DataType {
    productName?: string;
    imageUri?: string;
    email?: string;
    avatar?: string;
    id?: string;
  }

  const productData = [
    {
      id: 1,
      productName: "Health Care",
      description: "Magnet Software House CO., LTD",
      imageUri: HealthCare,
      active: false,
    },
    {
      id: 1,
      productName: "Health Care Partners",
      description: "Magnet Software House CO., LTD",
      imageUri: HCpartners,
      active: false,
    },
    {
      id: 2,
      productName: "ChangYai",
      description: "Magnet Software House CO., LTD",
      imageUri: CangYai,
      active: false,
    },
    {
      id: 2,
      productName: "ChangYai Partners",
      description: "Magnet Software House CO., LTD",
      imageUri: CYpartners,
      active: false,
    },
  ];

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DataType[]>([]);
  const [page, setPage] = useState(1);

  const loadMoreData = () => {
    if (loading) {
      return;
    }
    setLoading(true);
    fetch(
      `https://660d2bd96ddfa2943b33731c.mockapi.io/api/users/?page=${page}&limit=10`
    )
      .then((res) => res.json())
      .then((res) => {
        const results = Array.isArray(res) ? res : [];
        setData([...data, ...results]);
        setLoading(false);
        setPage(page + 1);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    loadMoreData();
  }, []);
  return (
    <div id="scrollableDiv">
      <div style={productClasses.containerButton}>
        <Button type="primary" href="/addNewProduct">
          <PlusCircleOutlined />
          New Project
        </Button>
      </div>

      <InfiniteScroll
        dataLength={productData.length}
        next={loadMoreData}
        hasMore={productData.length < 2}
        loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
        endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
        scrollableTarget="scrollableDiv"
      >
        <List
          dataSource={productData}
          renderItem={(item) => (
            <List.Item
              key={item.productName}
              actions={[<a key="list-loadmore-edit">edit</a>]}
            >
              <List.Item.Meta
                avatar={
                  <Image
                    src={item.imageUri}
                    alt="product"
                    width={45}
                    height={45}
                    style={productClasses.iconProduct}
                  />
                }
                title={<a href="https://ant.design">{item.productName}</a>}
                description={item.description}
              />
              <Tag color={item.active ? "green" : "gold"}>
                {item.active ? "Active" : "Developing"}
              </Tag>
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </div>
  );
};
