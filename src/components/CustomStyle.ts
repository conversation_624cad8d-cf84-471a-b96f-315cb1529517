import { useTheme } from "@/src/hooks/useTheme";
const { isDarkMode, toggleDarkMode } = useTheme();

export const loginClasses = {
  container:
    "flex min-h-screen flex-col justify-center items-center px-6 py-12 lg:px-8 bg-white",
  logo: "mb-6",
  fromContainer: {
    maxWidth: 360,
    width: "100%",
  },
};
export const productClasses = {
  containerButton: {
    display: "flex",
    justifyContent: "flex-end",
  },
  iconProduct: {
    borderRadius: "20%",
    borderWidth: 1,
    borderColor: "#f4f4f4",
  },
};
export const statisticClasses = {
  continueHeader: {
    paddingLeft: "10px",
    fontWeight: "bold" as const,
    fontSize: 16,
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    display: "flex" as const,
  },
  continueActiveUsers: {
    marginTop: 20,
    flexWrap: "nowrap" as const,
    overflowX: "auto" as const,
  },
  contunueSpan: { color: isDarkMode ? "#fff" : "#000" },
};
