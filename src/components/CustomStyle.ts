export const loginClasses = {
  logo: "mb-6",
  fromContainer: {
    maxWidth: 360,
    width: "100%",
  },
};

export const getContinueLogin = (isDarkMode: boolean) => ({
  container: {
    display: "flex",
    minHeight: "100vh",
    flexDirection: "column" as const,
    justifyContent: "center",
    alignItems: "center",
    padding: "48px 24px",
    background: isDarkMode ? "#171717" : "#fff",
    color: isDarkMode ? "#ededed" : "#171717",
    transition: "background-color 0.3s ease, color 0.3s ease",
  },
});
export const dashBoardClasses = {
  container: {
    minHeight: "100vh",
  },
  containerHeader: {
    display: "flex" as const,
    flexDirection: "column" as const,
  },
  buttomSpan: {
    fontSize: "16px",
    width: 64,
    height: 64,
  },
};

export const productClasses = {
  containerButton: {
    display: "flex",
    justifyContent: "flex-end",
  },
  iconProduct: {
    borderRadius: "20%",
    borderWidth: 1,
    borderColor: "#f4f4f4",
  },
};

export const statisticClasses = {
  continueHeader: {
    paddingLeft: "10px",
    fontWeight: "bold" as const,
    fontSize: 16,
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    display: "flex" as const,
  },
  continueActiveUsers: {
    marginTop: 20,
    flexWrap: "nowrap" as const,
    overflowX: "auto" as const,
  },
  continueSearch: {
    display: "flex",
    justifyContent: "flex-end",
  },
  continueInfiniteScroll: {
    height: 400,
    overflow: "auto",
    padding: "0 16px",
    marginTop: "20px",
  },
  boxSearch: {
    width: 300,
    marginTop: "20px",
  },
  card: {
    flex: "1 0 0",
    minWidth: 180,
  },
};


export const getStatisticSpanStyle = (isDarkMode: boolean) => ({
  color: isDarkMode ? "#fff" : "#000",
});
export const getStatisticValueStyle = (isDarkMode: boolean) => ({
  color: isDarkMode ? "#ededed" : "#000",
});
export const getStatisticBoxStyle = (isDarkMode: boolean) => ({
  background: isDarkMode ? "#171717" : "#fff",
  borderRadius: 20,
  padding: 20,
  border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
});
export const getDashBoardHeaderStyle = (colorBgContainer: string) => ({
  padding: "0 16px",
  background: colorBgContainer,
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
});
export const getDasBordContent = (borderRadiusLG: number, isDarkMode: boolean) => ({
  padding: 24,
  minHeight: "calc(100vh - 200px)",
  background: isDarkMode ? "#171717" : "#ffffff",
  borderRadius: borderRadiusLG,
});
