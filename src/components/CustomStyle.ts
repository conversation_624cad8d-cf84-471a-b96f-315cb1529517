import { AnyARecord } from "node:dns";

export const loginClasses = {
  logo: "mb-6",
  fromContainer: {
    maxWidth: 360,
    width: "100%",
  },
};

export const dashBoardClasses = {
  container: {
    minHeight: "100vh",
  },
  containerMenu: {
    display: "flex",
    alignItems: "center",
    gap: "16px",
  },
  containerHeader: {
    display: "flex" as const,
    flexDirection: "column" as const,
  },
  buttomSpan: {
    fontSize: "16px",
    width: 64,
    height: 64,
  },
  iconNoti: {
    fontSize: 16,
    cursor: "pointer",
  },
  content: {
    flex: 1,
    margin: "10px 16px",
  },
  textCenter: {
    textAlign: "center" as const,
  },
};

export const productClasses = {
  containerButton: {
    display: "flex",
    alignItems: "flex-end",
    justifyContent: "space-between",
    gap: "16px",
  },
  iconProduct: {
    borderRadius: "20%",
    borderWidth: 1,
    borderColor: "#f4f4f4",
  }
};

export const financeClasses = {
  container: {
    display: "flex",
    alignItems: "flex-end",
    gap: "16px",
  },
  width: {
    width: 200,
  }
};

export const statisticClasses = {
  continueHeader: {
    paddingLeft: "10px",
    fontWeight: "bold" as const,
    fontSize: 16,
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    display: "flex" as const,
  },
  continueActiveUsers: {
    marginTop: 20,
    flexWrap: "nowrap" as const,
    overflowX: "auto" as const,
  },
  continueSearch: {
    display: "flex",
    justifyContent: "flex-end",
  },
  continueInfiniteScroll: {
    height: 400,
    overflow: "auto",
    padding: "0 16px",
    marginTop: "20px",
  },
  boxSearch: {
    width: 300,
    marginTop: "20px",
  },
  card: {
    flex: "1 0 0",
    minWidth: 180,
  },
};

export const chartClasses = {
  container: {
    marginBottom: 24,
    marginTop: 20,
  },
  containerChart: {
    display: "flex",
    flexDirection: "column" as const,
    alignItems: "center",
    flex: 1,
    marginLeft: "40px",
  },
  padding: {
    padding: "20px 0",
  },
  disPlay: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
    gap: "16px",
  },
  textTotal: {
    fontSize: "24px",
    fontWeight: "bold",
    color: "#1890ff",
  },
  centerText: {
    textAlign: "center",
  },
  coreY: {
    position: "absolute" as const,
    left: "10px",
    top: "20px",
    display: "flex",
    flexDirection: "column-reverse" as const,
    height: "260px",
    justifyContent: "space-between",
  },
  textCenter: {
    textAlign: "center" as const,
  },
};

export const getContinueLogin = (isDarkMode: boolean) => ({
  container: {
    display: "flex",
    minHeight: "100vh",
    flexDirection: "column" as const,
    justifyContent: "center",
    alignItems: "center",
    padding: "48px 24px",
    background: isDarkMode ? "#171717" : "#fff",
    color: isDarkMode ? "#ededed" : "#171717",
    transition: "background-color 0.3s ease, color 0.3s ease",
  },
});
export const getStatisticSpanStyle = (isDarkMode: boolean) => ({
  color: isDarkMode ? "#fff" : "#000",
});
export const getStatisticValueStyle = (isDarkMode: boolean) => ({
  color: isDarkMode ? "#ededed" : "#000",
});
export const getStatisticBoxStyle = (isDarkMode: boolean) => ({
  background: isDarkMode ? "#171717" : "#fff",
  borderRadius: 20,
  padding: 20,
  border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
});
export const getDashBoardHeaderStyle = (colorBgContainer: string) => ({
  padding: "0 16px",
  background: colorBgContainer,
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
});
export const getDasBordContent = (
  borderRadiusLG: number,
  isDarkMode: boolean
) => ({
  padding: 24,
  minHeight: "calc(100vh - 200px)",
  background: isDarkMode ? "#171717" : "#ffffff",
  borderRadius: borderRadiusLG,
});
export const getChartBoxTotal = (isDarkMode: boolean) => ({
  marginBottom: "30px",
  padding: "20px",
  backgroundColor: isDarkMode ? "#1f1f1f" : "#f0f9ff",
  borderRadius: "12px",
  border: `2px solid ${isDarkMode ? "#374151" : "#bfdbfe"}`,
});
export const getChartdescription = (isDarkMode: boolean) => ({
  color: isDarkMode ? "#ededed" : "#666",
  fontSize: "14px",
});
export const getChartText12 = (isDarkMode: boolean) => ({
  marginBottom: "20px",
  color: isDarkMode ? "#ededed" : "#333",
  textAlign: "center" as const,
});
export const getChartcontainer = (isDarkMode: boolean) => ({
  display: "flex",
  alignItems: "end",
  justifyContent: "space-between",
  gap: "8px",
  padding: "20px",
  backgroundColor: isDarkMode ? "#262626" : "#fafafa",
  borderRadius: "12px",
  minHeight: "300px",
  position: "relative" as const,
});
export const getChartValue = (isDarkMode: boolean) => ({
  fontSize: "12px",
  color: isDarkMode ? "#999" : "#666",
  textAlign: "right" as const,
  width: "30px",
});
export const getChartStyle = (height: number) => ({
  width: "100%",
  maxWidth: "40px",
  height: `${height}px`,
  backgroundColor: "#1890ff",
  borderRadius: "4px 4px 0 0",
  position: "relative" as const,
  transition: "all 0.3s ease",
  cursor: "pointer",
  boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
});
export const getChartKnumber = (isDarkMode: boolean) => ({
  position: "absolute" as const,
  top: "-25px",
  left: "50%",
  transform: "translateX(-50%)",
  fontSize: "11px",
  fontWeight: "bold",
  color: isDarkMode ? "#ededed" : "#333",
  whiteSpace: "nowrap",
});
export const getChartMouth = (isDarkMode: boolean) => ({
  marginTop: "8px",
  fontSize: "12px",
  color: isDarkMode ? "#999" : "#666",
  textAlign: "center" as const,
  fontWeight: "500",
});
