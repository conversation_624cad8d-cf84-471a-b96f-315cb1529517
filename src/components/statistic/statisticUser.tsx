import React, { useEffect, useState } from "react";
import type { StatisticProps } from "antd";
import {
  Col,
  Row,
  Statistic,
  Select,
  List,
  Skeleton,
  Avatar,
  Divider,
} from "antd";
import InfiniteScroll from "react-infinite-scroll-component";
import CountUp from "react-countup";
import { AutoComplete, Input } from "antd";
import type { AutoCompleteProps } from "antd";
import { statisticClasses } from "../CustomStyle";
import { useTheme } from "../../hooks/useTheme";

interface DataType {
  gender?: string;
  name?: string;
  email?: string;
  avatar?: string;
  id?: string;
}

export const StatisticUser = () => {
  const defaultIndex = 0;
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DataType[]>([]);
  const [hasMoreData, setHasMoreData] = useState(true);
  const { isDarkMode } = useTheme();
  const [options, setOptions] = useState<AutoCompleteProps["options"]>([]);

  const formatter: StatisticProps["formatter"] = (value) => (
    <CountUp end={value as number} separator="," />
  );
  const getRandomInt = (max: number, min = 0) =>
    Math.floor(Math.random() * (max - min + 1)) + min;

  const Selectproduct = [
    { id: 1, productName: "Health Care" },
    { id: 2, productName: "ChangYai" },
  ];

  useEffect(() => {
    loadMoreData();
  }, []);
  const loadMoreData = async () => {
    if (loading) return;
    setLoading(true);

    const res = await fetch(
      `https://660d2bd96ddfa2943b33731c.mockapi.io/api/users/?page=${page}&limit=10`
    );
    const newData = await res.json();

    setData((prev) => [...prev, ...newData]);
    setHasMoreData(newData.length > 0);
    setPage((prev) => prev + 1);
    setLoading(false);
  };
  const handleSearch = (value: string) => {
    setOptions(value ? searchResult(value) : []);
  };
  const onSelect = (value: string) => {
    console.log("onSelect", value);
  };
  const searchResult = (query: string) =>
    Array.from({ length: getRandomInt(5) })
      .join(".")
      .split(".")
      .map((_, idx) => {
        const category = `${query}${idx}`;
        return {
          value: category,
          label: (
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
              }}
            >
              <span>
                Found {query} on{" "}
                <a
                  href={`https://s.taobao.com/search?q=${query}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {category}
                </a>
              </span>
              <span>{getRandomInt(200, 100)} results</span>
            </div>
          ),
        };
      });

  return (
    <>
      <div style={statisticClasses.continueHeader}>
        Active Users
        <Select
          showSearch
          style={{ width: 200 }}
          placeholder="Search to Select"
          optionFilterProp="label"
          defaultValue={Selectproduct[defaultIndex]?.id}
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.label ?? "")
              .toLowerCase()
              .localeCompare((optionB?.label ?? "").toLowerCase())
          }
          options={Selectproduct.map((item) => ({
            value: item.id,
            label: item.productName,
          }))}
        />
      </div>
      <Row gutter={8} style={statisticClasses.continueActiveUsers}>
        <Col style={{ flex: "1 0 0", minWidth: 180 }}>
          <Statistic
            title={
              <span style={statisticClasses.contunueSpan}>Google Users</span>
            }
            value={12890}
            formatter={formatter}
            valueStyle={{ color: isDarkMode ? "#ededed" : "#000" }}
            style={{
              background: isDarkMode ? "#171717" : "#fff",
              borderRadius: 20,
              padding: 20,
              border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
            }}
          />
        </Col>
        <Col style={{ flex: "1 0 0", minWidth: 180 }}>
          <Statistic
            title={
              <span style={statisticClasses.contunueSpan}>Facebook Users</span>
            }
            value={21230}
            formatter={formatter}
            valueStyle={{ color: isDarkMode ? "#ededed" : "#000" }}
            style={{
              background: isDarkMode ? "#171717" : "#fff",
              borderRadius: 20,
              padding: 20,
              border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
            }}
          />
        </Col>
        <Col style={{ flex: "1 0 0", minWidth: 180 }}>
          <Statistic
            title={
              <span style={statisticClasses.contunueSpan}>Apple Users</span>
            }
            value={7654}
            formatter={formatter}
            valueStyle={{ color: isDarkMode ? "#ededed" : "#000" }}
            style={{
              background: isDarkMode ? "#171717" : "#fff",
              borderRadius: 20,
              padding: 20,
              border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
            }}
          />
        </Col>
        <Col style={{ flex: "1 0 0", minWidth: 180 }}>
          <Statistic
            title={
              <span style={statisticClasses.contunueSpan}>Email Users</span>
            }
            value={9876}
            formatter={formatter}
            valueStyle={{ color: isDarkMode ? "#ededed" : "#000" }}
            style={{
              background: isDarkMode ? "#171717" : "#fff",
              borderRadius: 20,
              padding: 20,
              border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
            }}
          />
        </Col>
        <Col style={{ flex: "1 0 0", minWidth: 180 }}>
          <Statistic
            title={
              <span style={statisticClasses.contunueSpan}>Phone Users</span>
            }
            value={5400}
            formatter={formatter}
            valueStyle={{ color: isDarkMode ? "#ededed" : "#000" }}
            style={{
              background: isDarkMode ? "#171717" : "#fff",
              borderRadius: 20,
              padding: 20,
              border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
            }}
          />
        </Col>
        <Col style={{ flex: "1 0 0", minWidth: 180 }}>
          <Statistic
            title={
              <span style={statisticClasses.contunueSpan}>Partners Users</span>
            }
            value={9876}
            formatter={formatter}
            valueStyle={{ color: isDarkMode ? "#ededed" : "#000" }}
            style={{
              background: isDarkMode ? "#171717" : "#fff",
              borderRadius: 20,
              padding: 20,
              border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
            }}
          />
        </Col>
        <Col style={{ flex: "1 0 0", minWidth: 180 }}>
          <Statistic
            title={
              <span style={statisticClasses.contunueSpan}>Admin Users</span>
            }
            value={5400}
            formatter={formatter}
            valueStyle={{ color: isDarkMode ? "#ededed" : "#000" }}
            style={{
              background: isDarkMode ? "#171717" : "#fff",
              borderRadius: 20,
              padding: 20,
              border: isDarkMode ? "1px solid #374151" : "1px solid #f4f4f4",
            }}
          />
        </Col>
      </Row>

      <div style={{ display: "flex", justifyContent: "flex-end" }}>
        <AutoComplete
          popupMatchSelectWidth={252}
          style={{ width: 300, marginTop: "20px" }}
          options={options}
          onSelect={onSelect}
          onSearch={handleSearch}
        >
          <Input.Search size="large" placeholder="Search here" enterButton />
        </AutoComplete>
      </div>

      <div
        id="scrollableDiv"
        style={{
          height: 500,
          overflow: "auto",
          padding: "0 16px",
          marginTop: "20px",
        }}
      >
        <InfiniteScroll
          dataLength={data.length}
          next={loadMoreData}
          hasMore={hasMoreData}
          loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
          endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
          scrollableTarget="scrollableDiv"
        >
          <List
            dataSource={data}
            renderItem={(item) => (
              <List.Item
                key={item.email}
                actions={[<a key="list-loadmore-edit">edit</a>]}
              >
                <List.Item.Meta
                  avatar={<Avatar src={item.avatar} />}
                  title={<a href="https://ant.design">{item.name}</a>}
                  description={item.email}
                />
              </List.Item>
            )}
          />
        </InfiniteScroll>
      </div>
    </>
  );
};
