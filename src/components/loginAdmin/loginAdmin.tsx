"use client";

import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import Logoapp from "../../assets/images/logoapp.png";
import { Button, Checkbox, Form, Input, Flex } from "antd";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { loginClasses, getContinueLogin } from "../CustomStyle";
import { useTheme } from "../../hooks/useTheme";

export const LogingAdmin: React.FC = () => {
  const router = useRouter();
  const { isDarkMode } = useTheme();
  const onFinish = (values: any) => {
    router.push("/firstScreen");
  };

  return (
    <div style={getContinueLogin(isDarkMode).container}>
      <Image
        src={Logoapp}
        alt="Logo"
        width={300}
        height={300}
        className={loginClasses.logo}
      />
      <Form
        name="login"
        initialValues={{ remember: true }}
        style={loginClasses.fromContainer}
        onFinish={onFinish}
      >
        <Form.Item
          name="username"
          rules={[{ required: true, message: "Please input your Username!" }]}
        >
          <Input prefix={<UserOutlined />} placeholder="Username" />
        </Form.Item>
        <Form.Item
          name="password"
          rules={[{ required: true, message: "Please input your Password!" }]}
        >
          <Input
            prefix={<LockOutlined />}
            type="password"
            placeholder="Password"
          />
        </Form.Item>
        <Form.Item>
          <Flex justify="space-between" align="center">
            <Form.Item name="remember" valuePropName="checked" noStyle>
              <Checkbox>Remember me</Checkbox>
            </Form.Item>
            <a href="">Forgot password</a>
          </Flex>
        </Form.Item>

        <Form.Item>
          <Button block type="primary" htmlType="submit">
            Log in
          </Button>
          or <a href="">Register now!</a>
        </Form.Item>
      </Form>
    </div>
  );
};

export default LogingAdmin;
