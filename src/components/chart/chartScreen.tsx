import React from "react";
import { Card } from "antd";
import { useTheme } from "../../hooks/useTheme";

export default function MonthlyRevenueChart() {
  const { isDarkMode } = useTheme();

  // รายได้ 12 เดือน (หน่วย: พันบาท)
  const currentYear = new Date().getFullYear();

  const revenueData = {
    revenue: [120, 150, 180, 200, 175, 220, 250, 280, 240, 300, 320, 350], // รายได้แต่ละเดือน
    labels: [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ],
    // colors: [
    //   '#1890ff', '#13c2c2', '#52c41a', '#faad14',
    //   '#f759ab', '#722ed1', '#fa541c', '#eb2f96',
    //   '#2f54eb', '#fa8c16', '#a0d911', '#ff4d4f'
    // ]
    colors: "#1890ff",
  };

  // คำนวณสถิติ
  const totalRevenue = revenueData.revenue.reduce(
    (sum, value) => sum + value,
    0
  );
  const averageRevenue = Math.round(totalRevenue / 12);
  const maxRevenue = Math.max(...revenueData.revenue);
  const maxMonth = revenueData.labels[revenueData.revenue.indexOf(maxRevenue)];
  return (
    <Card
      title="Monthly income"
      style={{ marginBottom: 24, marginTop: 20 }}
      extra={<span style={{ fontSize: "14px" }}>Year {currentYear}</span>}
    >
      <div style={{ padding: "20px 0" }}>
        {/* สถิติรวม */}
        <div
          style={{
            marginBottom: "30px",
            padding: "20px",
            backgroundColor: isDarkMode ? "#1f1f1f" : "#f0f9ff",
            borderRadius: "12px",
            border: `2px solid ${isDarkMode ? "#374151" : "#bfdbfe"}`,
          }}
        >
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
              gap: "16px",
            }}
          >
            <div style={{ textAlign: "center" }}>
              <div
                style={{
                  fontSize: "24px",
                  fontWeight: "bold",
                  color: "#1890ff",
                }}
              >
                {totalRevenue.toLocaleString()}K
              </div>
              <div
                style={{
                  color: isDarkMode ? "#ededed" : "#666",
                  fontSize: "14px",
                }}
              >
                Total income for the year
              </div>
            </div>
            <div style={{ textAlign: "center" }}>
              <div
                style={{
                  fontSize: "24px",
                  fontWeight: "bold",
                  color: "#1890ff",
                }}
              >
                {averageRevenue.toLocaleString()}K
              </div>
              <div
                style={{
                  color: isDarkMode ? "#ededed" : "#666",
                  fontSize: "14px",
                }}
              >
                Average monthly income
              </div>
            </div>
            <div style={{ textAlign: "center" }}>
              <div
                style={{
                  fontSize: "24px",
                  fontWeight: "bold",
                  color: "#1890ff",
                }}
              >
                {maxRevenue.toLocaleString()}K
              </div>
              <div
                style={{
                  color: isDarkMode ? "#ededed" : "#666",
                  fontSize: "14px",
                }}
              >
                Highest income ({maxMonth})
              </div>
            </div>
          </div>
        </div>

        {/* กราฟแท่ง */}
        <h3
          style={{
            marginBottom: "20px",
            color: isDarkMode ? "#ededed" : "#333",
            textAlign: "center",
          }}
        >
          12 Months Income Bar Chart
        </h3>

        <div
          style={{
            display: "flex",
            alignItems: "end",
            justifyContent: "space-between",
            gap: "8px",
            padding: "20px",
            backgroundColor: isDarkMode ? "#262626" : "#fafafa",
            borderRadius: "12px",
            minHeight: "300px",
            position: "relative",
          }}
        >
          {/* แกน Y (ป้ายกำกับ) */}
          <div
            style={{
              position: "absolute",
              left: "10px",
              top: "20px",
              display: "flex",
              flexDirection: "column-reverse",
              height: "260px",
              justifyContent: "space-between",
            }}
          >
            {[0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000].map(
              (value) => (
                <div
                  key={value}
                  style={{
                    fontSize: "12px",
                    color: isDarkMode ? "#999" : "#666",
                    textAlign: "right",
                    width: "30px",
                  }}
                >
                  {value}K
                </div>
              )
            )}
          </div>

          {/* แท่งกราฟ */}
          {revenueData.revenue.map((revenue, index) => {
            const height = (revenue / 400) * 240; // สูงสุด 240px
            return (
              <div
                key={index}
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  flex: 1,
                  marginLeft: "40px",
                }}
              >
                {/* แท่ง */}
                <div
                  style={{
                    width: "100%",
                    maxWidth: "40px",
                    height: `${height}px`,
                    // backgroundColor: revenueData.colors[index],
                    backgroundColor: revenueData.colors,
                    borderRadius: "4px 4px 0 0",
                    position: "relative",
                    transition: "all 0.3s ease",
                    cursor: "pointer",
                    boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "scale(1.05)";
                    e.currentTarget.style.boxShadow =
                      "0 4px 16px rgba(0,0,0,0.2)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "scale(1)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 8px rgba(0,0,0,0.1)";
                  }}
                >
                  {/* ค่าบนแท่ง */}
                  <div
                    style={{
                      position: "absolute",
                      top: "-25px",
                      left: "50%",
                      transform: "translateX(-50%)",
                      fontSize: "11px",
                      fontWeight: "bold",
                      color: isDarkMode ? "#ededed" : "#333",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {revenue}K
                  </div>
                </div>

                {/* ป้ายเดือน */}
                <div
                  style={{
                    marginTop: "8px",
                    fontSize: "12px",
                    color: isDarkMode ? "#999" : "#666",
                    textAlign: "center",
                    fontWeight: "500",
                  }}
                >
                  {revenueData.labels[index]}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </Card>
  );
}
