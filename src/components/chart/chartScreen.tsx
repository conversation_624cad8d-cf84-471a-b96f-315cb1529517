import React from "react";
import { Card } from "antd";
import { useTheme } from "../../hooks/useTheme";
import {
  chartClasses,
  getChartMouth,
  getChartStyle,
  getChartValue,
  getChartText12,
  getChartKnumber,
  getChartBoxTotal,
  getChartcontainer,
  getChartdescription,
} from "../CustomStyle";

export default function MonthlyRevenueChart() {
  const { isDarkMode } = useTheme();

  // รายได้ 12 เดือน (หน่วย: พันบาท)
  const currentYear = new Date().getFullYear();

  const revenueData = {
    revenue: [120, 150, 180, 200, 175, 220, 250, 280, 240, 300, 320, 350], // รายได้แต่ละเดือน
    labels: [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ],
  };

  // คำนวณสถิติ
  const totalRevenue = revenueData.revenue.reduce(
    (sum, value) => sum + value,
    0
  );
  const averageRevenue = Math.round(totalRevenue / 12);
  const maxRevenue = Math.max(...revenueData.revenue);
  const maxMonth = revenueData.labels[revenueData.revenue.indexOf(maxRevenue)];
  return (
    <Card
      title="Monthly income"
      style={chartClasses.container}
      extra={<span>Year {currentYear}</span>}
    >
      <div style={chartClasses.padding}>
        {/* สถิติรวม */}
        <div style={getChartBoxTotal(isDarkMode)}>
          <div style={chartClasses.disPlay}>
            <div style={chartClasses.textCenter}>
              <div style={chartClasses.textTotal}>
                {totalRevenue.toLocaleString()}K
              </div>
              <div style={getChartdescription(isDarkMode)}>
                Total income for the year
              </div>
            </div>
            <div style={chartClasses.textCenter}>
              <div style={chartClasses.textTotal}>
                {averageRevenue.toLocaleString()}K
              </div>
              <div style={getChartdescription(isDarkMode)}>
                Average monthly income
              </div>
            </div>
            <div style={chartClasses.textCenter}>
              <div style={chartClasses.textTotal}>
                {maxRevenue.toLocaleString()}K
              </div>
              <div style={getChartdescription(isDarkMode)}>
                Highest income ({maxMonth})
              </div>
            </div>
          </div>
        </div>

        {/* กราฟแท่ง */}
        <div style={getChartText12(isDarkMode)}>12 Months Income Bar Chart</div>
        <div style={getChartcontainer(isDarkMode)}>
          {/* แกน Y (ป้ายกำกับ) */}
          <div style={chartClasses.coreY}>
            {[0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000].map(
              (value) => (
                <div key={value} style={getChartValue(isDarkMode)}>
                  {value}K
                </div>
              )
            )}
          </div>

          {/* แท่งกราฟ */}
          {revenueData.revenue.map((revenue, index) => {
            const height = (revenue / 400) * 240; // สูงสุด 240px
            return (
              <div key={index} style={chartClasses.containerChart}>
                {/* แท่ง */}
                <div
                  style={getChartStyle(height)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "scale(1.05)";
                    e.currentTarget.style.boxShadow =
                      "0 4px 16px rgba(0,0,0,0.2)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "scale(1)";
                    e.currentTarget.style.boxShadow =
                      "0 2px 8px rgba(0,0,0,0.1)";
                  }}
                >
                  {/* ค่าบนแท่ง */}
                  <div style={getChartKnumber(isDarkMode)}>{revenue}K</div>
                </div>

                {/* ป้ายเดือน */}
                <div style={getChartMouth(isDarkMode)}>
                  {revenueData.labels[index]}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </Card>
  );
}
