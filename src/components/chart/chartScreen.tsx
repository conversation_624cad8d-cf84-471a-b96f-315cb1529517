import React from "react";
import { Card } from "antd";
import { useTheme } from "../../hooks/useTheme";

export default function CardLineChart() {
  const { isDarkMode } = useTheme();

  // Sample data for the chart
  const currentYear = new Date().getFullYear();
  const previousYear = currentYear - 1;

  const chartData = {
    current: [65, 78, 66, 44, 56, 67, 75],
    previous: [40, 68, 86, 74, 56, 60, 87],
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
  };
  return (
    <Card
      title="Sales Overview"
      style={{ marginBottom: 24 }}
      extra={<span style={{ fontSize: '14px', color: '#666' }}>Monthly Data</span>}
    >
      <div style={{ padding: '20px 0' }}>
        <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>Sales Value Comparison</h3>

        {/* Current Year Data */}
        <div style={{ marginBottom: '20px' }}>
          <h4 style={{ marginBottom: '8px' }}>{currentYear} Sales</h4>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            {chartData.labels.map((month, index) => (
              <div
                key={month}
                style={{
                  padding: '8px 12px',
                  backgroundColor: '#3182ce',
                  color: 'white',
                  borderRadius: '4px',
                  fontSize: '12px',
                  minWidth: '60px',
                  textAlign: 'center'
                }}
              >
                <div>{month}</div>
                <div style={{ fontWeight: 'bold' }}>{chartData.current[index]}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Previous Year Data */}
        <div>
          <h4 style={{ marginBottom: '8px' }}>{currentYear - 1} Sales</h4>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            {chartData.labels.map((month, index) => (
              <div
                key={month}
                style={{
                  padding: '8px 12px',
                  backgroundColor: '#edf2f7',
                  color: '#333',
                  borderRadius: '4px',
                  fontSize: '12px',
                  minWidth: '60px',
                  textAlign: 'center'
                }}
              >
                <div>{month}</div>
                <div style={{ fontWeight: 'bold' }}>{chartData.previous[index]}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Summary */}
        <div style={{
          marginTop: '20px',
          padding: '16px',
          backgroundColor: isDarkMode ? '#1f1f1f' : '#f5f5f5',
          borderRadius: '8px'
        }}>
          <h4 style={{
            marginBottom: '8px',
            color: isDarkMode ? '#ededed' : '#333'
          }}>Summary</h4>
          <div style={{ display: 'flex', gap: '20px' }}>
            <div>
              <span style={{ color: '#666' }}>Total {currentYear}: </span>
              <span style={{ fontWeight: 'bold', color: '#3182ce' }}>
                {chartData.current.reduce((a, b) => a + b, 0)}
              </span>
            </div>
            <div>
              <span style={{ color: '#666' }}>Total {currentYear - 1}: </span>
              <span style={{ fontWeight: 'bold', color: '#666' }}>
                {chartData.previous.reduce((a, b) => a + b, 0)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}