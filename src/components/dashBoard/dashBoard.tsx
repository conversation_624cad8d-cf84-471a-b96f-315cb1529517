"use client";

import React, { useState } from "react";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  Pie<PERSON><PERSON>Outlined,
  TeamOutlined,
  UserOutlined,
  ProductOutlined,
  LogoutOutlined,
  SettingOutlined,
  MoonOutlined,
  SunOutlined,
  HistoryOutlined,
  BankOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  InboxOutlined,
} from "@ant-design/icons";
import type { MenuProps } from "antd";
import {
  Button,
  Layout,
  Menu,
  theme,
  ConfigProvider,
  Badge,
  Space,
  Avatar,
} from "antd";
import { ProductList } from "../product/productList";
import { StatisticUser } from "../statistic/statisticUser";
import { SettingScreen } from "@/src/screen/setting/settingScreen";
import { Approve_reject } from "../history/approve_reject";
import { Document } from "../history/document";
import { useTheme } from "@/src/hooks/useTheme";
import { useRouter } from "next/navigation";
import {
  dashBoardClasses,
  getDashBoardHeaderStyle,
  getDasBordContent,
} from "../CustomStyle";

const { Header, Content, Footer, Sider } = Layout;

type MenuItem = Required<MenuProps>["items"][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem("User", "sub1", <UserOutlined />, [
    getItem("Active  Users", "2"),
    getItem("Approve Partners", "3"),
  ]),

  getItem("Product", "4", <ProductOutlined />),
  getItem("History", "sub2", <HistoryOutlined />, [
    getItem("Approve & Reject", "5"),
    getItem("Document", "6"),
  ]),
  getItem("Setting", "7", <SettingOutlined />),
  getItem("Logout", "8", <LogoutOutlined />),
];

const DashBord: React.FC = () => {
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState("2");
  const { isDarkMode, toggleDarkMode } = useTheme();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const renderContent = () => {
    switch (selectedKey) {
      case "2":
        return <StatisticUser />;
      case "3":
        return <div>Approve Partners</div>;
      case "4":
        return <ProductList />;
      case "5":
        return <Approve_reject />;
      case "6":
        return <Document />;
      case "7":
        return <div>Welcome</div>;
      case "8":
        return <div>Welcome</div>;
      default:
        return <div>Welcome</div>;
    }
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
      }}
    >
      <Layout style={dashBoardClasses.container}>
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          onCollapse={(value) => setCollapsed(value)}
        >
          <div className="demo-logo-vertical" />
          <Menu
            theme="dark"
            mode="inline"
            defaultSelectedKeys={["2"]}
            selectedKeys={[selectedKey]}
            items={items}
            onClick={(e) => setSelectedKey(e.key)}
          />
        </Sider>
        <Layout style={dashBoardClasses.containerHeader}>
          <Header style={getDashBoardHeaderStyle(colorBgContainer)}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={dashBoardClasses.buttomSpan}
            />

            <Space size="middle">
              <Badge count={3} size="small">
                <InboxOutlined
                  style={{ fontSize: 16 }}
                  onClick={() => router.push("/approve_reject")}
                />
              </Badge>

              <Badge count={5} size="small">
                <MessageOutlined
                  style={{ fontSize: 16 }}
                  onClick={() => router.push("/approve_reject")}
                />
              </Badge>

              <Badge count={2} size="small">
                <CheckCircleOutlined
                  style={{ fontSize: 16 }}
                  onClick={() => router.push("/document")}
                />
              </Badge>
            </Space>

            <Button
              type="text"
              icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleDarkMode}
              style={dashBoardClasses.buttomSpan}
              title={
                isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"
              }
            />
          </Header>

          <Content style={{ flex: 1, margin: "10px 16px" }}>
            <div style={getDasBordContent(borderRadiusLG, isDarkMode)}>
              {renderContent()}
            </div>
          </Content>

          <Footer style={{ textAlign: "center" }}>
            Magnet Software.CO., LTD
          </Footer>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default DashBord;
