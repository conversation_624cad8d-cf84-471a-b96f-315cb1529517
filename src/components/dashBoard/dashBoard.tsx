"use client";

import React, { useState } from "react";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  ProductOutlined,
  LogoutOutlined,
  SettingOutlined,
  MoonOutlined,
  SunOutlined,
  HistoryOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  InboxOutlined,
  NotificationOutlined,
  FileDoneOutlined,
  FileOutlined,
  AuditOutlined,
  Line<PERSON>hartOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import type { MenuProps } from "antd";
import {
  Button,
  Layout,
  Menu,
  theme,
  ConfigProvider,
  Badge,
  Space,
  Avatar,
  Modal,
} from "antd";
import { ProductList } from "../product/productList";
import { StatisticUser } from "../statistic/statisticUser";
import { SettingScreen } from "@/src/screen/setting/settingScreen";
import { Approve_reject } from "../history/approve_reject";
import { Document } from "../history/document";
import { useTheme } from "@/src/hooks/useTheme";
import { useRouter } from "next/navigation";
import { Message } from "../notification/message";
import { Report } from "../notification/report";
import { AppProve_Partners } from "../appProve/appProve_Partners";
import { Finance } from "../../components/finance/finance";
import { Employee } from "../statistic/employee";
import {
  dashBoardClasses,
  getDashBoardHeaderStyle,
  getDasBordContent,
} from "../CustomStyle";

const { Header, Content, Footer, Sider } = Layout;

type MenuItem = Required<MenuProps>["items"][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem("Finance", "1", <LineChartOutlined />),
  getItem("Manage User", "sub1", <UserOutlined />, [
    getItem("Project Users", "2", <AuditOutlined />),
    getItem("Employee Magnet", "3", <TeamOutlined />),
  ]),
  getItem("Approve", "4", <FileDoneOutlined />),
  getItem("Product", "5", <ProductOutlined />),
  getItem("History", "sub2", <HistoryOutlined />, [
    getItem("Approve & Reject", "6", <CheckCircleOutlined />),
    getItem("Document", "7", <FileOutlined />),
  ]),
  getItem("Notification", "sub3", <NotificationOutlined />, [
    getItem("Message", "8", <MessageOutlined />),
    getItem("Report", "9", <InboxOutlined />),
  ]),
  getItem("Setting", "10", <SettingOutlined />),
  getItem("Logout", "11", <LogoutOutlined />),
];

const DashBord: React.FC = () => {
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState("2");
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const { isDarkMode, toggleDarkMode } = useTheme();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const showLogoutModal = () => {
    setIsLogoutModalOpen(true);
  };
  const handleLogoutOk = () => {
    setIsLogoutModalOpen(false);
    router.push("/");
  };
  const handleLogoutCancel = () => {
    setIsLogoutModalOpen(false);
  };
  const handleMenuClick = (e: any) => {
    if (e.key === "11") {
      showLogoutModal();
    } else {
      setSelectedKey(e.key);
    }
  };

  const renderContent = () => {
    switch (selectedKey) {
      case "1":
        return <Finance />;
      case "2":
        return <StatisticUser />;
      case "3":
        return <Employee />;
      case "4":
        return <AppProve_Partners />;
      case "5":
        return <ProductList />;
      case "6":
        return <Approve_reject />;
      case "7":
        return <Document />;
      case "8":
        return <Message />;
      case "9":
        return <Report />;
      case "10":
        return <SettingScreen />;

      default:
        return <div>Welcome</div>;
    }
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
      }}
    >
      <Layout style={dashBoardClasses.container}>
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          onCollapse={(value) => setCollapsed(value)}
        >
          <div className="demo-logo-vertical" />
          <Menu
            theme="dark"
            mode="inline"
            defaultSelectedKeys={["2"]}
            selectedKeys={[selectedKey]}
            items={items}
            onClick={handleMenuClick}
          />
        </Sider>
        <Layout style={dashBoardClasses.containerHeader}>
          <Header style={getDashBoardHeaderStyle(colorBgContainer)}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={dashBoardClasses.buttomSpan}
            />

            <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
              <Space size="middle">
                <Badge count={5} size="small">
                  <MessageOutlined
                    style={{ fontSize: 16, cursor: "pointer" }}
                    onClick={() => setSelectedKey("8")}
                  />
                </Badge>

                <Badge count={3} size="small">
                  <InboxOutlined
                    style={{ fontSize: 16, cursor: "pointer" }}
                    onClick={() => setSelectedKey("9")}
                  />
                </Badge>

                <Badge count={2} size="small">
                  <FileDoneOutlined
                    style={{ fontSize: 16, cursor: "pointer" }}
                    onClick={() => setSelectedKey("4")}
                  />
                </Badge>
              </Space>

              <Button
                type="text"
                icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
                onClick={toggleDarkMode}
                style={dashBoardClasses.buttomSpan}
                title={
                  isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"
                }
              />
            </div>
          </Header>

          <Content style={{ flex: 1, margin: "10px 16px" }}>
            <div style={getDasBordContent(borderRadiusLG, isDarkMode)}>
              {renderContent()}
            </div>
          </Content>

          <Footer style={{ textAlign: "center" }}>
            Magnet Software House CO., LTD
          </Footer>
        </Layout>
      </Layout>

      <Modal
        title="Confirm Logout"
        open={isLogoutModalOpen}
        onOk={handleLogoutOk}
        onCancel={handleLogoutCancel}
        okText="OK"
        cancelText="Cancel"
        centered
      >
        <p>Are you sure you want to logout?</p>
      </Modal>
    </ConfigProvider>
  );
};

export default DashBord;
