"use client";

import React, { useState } from "react";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  Pie<PERSON><PERSON>Outlined,
  TeamOutlined,
  UserOutlined,
  ProductOutlined,
  LogoutOutlined,
  SettingOutlined,
  MoonOutlined,
  SunOutlined,
} from "@ant-design/icons";
import type { MenuProps } from "antd";
import { Button, Layout, Menu, theme, ConfigProvider } from "antd";
import { ProductList } from "../product/productList";
import { StatisticUser } from "../statistic/statisticUser";
import { SettingScreen } from "@/src/screen/setting/settingScreen";
import { useTheme } from "@/src/hooks/useTheme";

const { Header, Content, Footer, Sider } = Layout;

type MenuItem = Required<MenuProps>["items"][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem("User", "sub1", <UserOutlined />, [
    getItem("Active  Users", "2"),
    getItem("Approve Partners", "3"),
  ]),
  getItem("Product", "4", <ProductOutlined />),
  getItem("Setting", "5", <SettingOutlined />),
  getItem("Logout", "6", <LogoutOutlined />),
];

const DashBord: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState("2");
  const { isDarkMode, toggleDarkMode } = useTheme();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const renderContent = () => {
    switch (selectedKey) {
      case "2":
        return <StatisticUser />;
      case "3":
        return <div>Approve Partners</div>;
      case "4":
        return <ProductList />;
      case "5":
        return <SettingScreen />;
      case "6":
        return <div>Logging out...</div>;
      default:
        return <div>Welcome</div>;
    }
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
      }}
    >
      <Layout style={{ minHeight: "100vh" }}>
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          onCollapse={(value) => setCollapsed(value)}
        >
          <div className="demo-logo-vertical" />
          <Menu
            theme="dark"
            mode="inline"
            defaultSelectedKeys={["2"]}
            selectedKeys={[selectedKey]}
            items={items}
            onClick={(e) => setSelectedKey(e.key)}
          />
        </Sider>
        <Layout style={{ display: "flex", flexDirection: "column" }}>
          <Header
            style={{
              padding: 0,
              background: colorBgContainer,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: "16px",
                width: 64,
                height: 64,
              }}
            />
            <Button
              type="text"
              icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleDarkMode}
              style={{
                fontSize: "16px",
                width: 64,
                height: 64,
                marginRight: 16,
              }}
              title={
                isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"
              }
            />
          </Header>

          <Content style={{ flex: 1, margin: "10px 16px" }}>
            <div
              style={{
                padding: 24,
                height: "100%",
                background: isDarkMode? "#171717" : "#ffffff",
                borderRadius: borderRadiusLG,
              }}
            >
              {renderContent()}
            </div>
          </Content>

          <Footer style={{ textAlign: "center" }}>
            Magnet Software.CO., LTD
          </Footer>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default DashBord;
