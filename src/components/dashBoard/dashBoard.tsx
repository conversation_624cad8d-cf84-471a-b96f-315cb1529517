"use client";

import React, { useState } from "react";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  ProductOutlined,
  LogoutOutlined,
  SettingOutlined,
  MoonOutlined,
  SunOutlined,
  HistoryOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  InboxOutlined,
  NotificationOutlined,
  FileDoneOutlined,
  FileOutlined,
  AuditOutlined,
  LineChartOutlined,
} from "@ant-design/icons";
import type { MenuProps } from "antd";
import {
  Button,
  Layout,
  Menu,
  theme,
  ConfigProvider,
  Badge,
  Space,
  Avatar,
  Modal,
} from "antd";
import { ProductList } from "../product/productList";
import { StatisticUser } from "../statistic/statisticUser";
import { SettingScreen } from "@/src/screen/setting/settingScreen";
import { Approve_reject } from "../history/approve_reject";
import { Document } from "../history/document";
import { useTheme } from "@/src/hooks/useTheme";
import { useRouter } from "next/navigation";
import { Message } from "../notification/message";
import { Report } from "../notification/report";
import { AppProve_Partners } from "../statistic/appProve_Partners";
import { Finance } from "../../components/finance/finance";
import {
  dashBoardClasses,
  getDashBoardHeaderStyle,
  getDasBordContent,
} from "../CustomStyle";

const { Header, Content, Footer, Sider } = Layout;

type MenuItem = Required<MenuProps>["items"][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[]
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

const items: MenuItem[] = [
  getItem("Finance and Accounting", "1", <LineChartOutlined />),
  getItem("Manage User", "sub1", <UserOutlined />, [
    getItem("Audit  Users", "2", <AuditOutlined />),
    getItem("Approve Partners", "3", <FileDoneOutlined />),
  ]),
  getItem("Product", "4", <ProductOutlined />),
  getItem("History", "sub2", <HistoryOutlined />, [
    getItem("Approve & Reject", "5", <CheckCircleOutlined />),
    getItem("Document", "6", <FileOutlined />),
  ]),
  getItem("Notification", "sub3", <NotificationOutlined />, [
    getItem("Message", "7", <MessageOutlined />),
    getItem("Report", "8", <InboxOutlined />),
  ]),
  getItem("Setting", "9", <SettingOutlined />),
  getItem("Logout", "10", <LogoutOutlined />),
];

const DashBord: React.FC = () => {
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState("2");
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const { isDarkMode, toggleDarkMode } = useTheme();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const showLogoutModal = () => {
    setIsLogoutModalOpen(true);
  };
  const handleLogoutOk = () => {
    setIsLogoutModalOpen(false);
    router.push("/");
  };
  const handleLogoutCancel = () => {
    setIsLogoutModalOpen(false);
  };
  const handleMenuClick = (e: any) => {
    if (e.key === "10") {
      showLogoutModal();
    } else {
      setSelectedKey(e.key);
    }
  };

  const renderContent = () => {
    switch (selectedKey) {
      case "1":
        return <Finance />;
      case "2":
        return <StatisticUser />;
      case "3":
        return <AppProve_Partners />;
      case "4":
        return <ProductList />;
      case "5":
        return <Approve_reject />;
      case "6":
        return <Document />;
      case "7":
        return <Message />;
      case "8":
        return <Report />;
      case "9":
        return <SettingScreen />;

      default:
        return <div>Welcome</div>;
    }
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
      }}
    >
      <Layout style={dashBoardClasses.container}>
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          onCollapse={(value) => setCollapsed(value)}
        >
          <div className="demo-logo-vertical" />
          <Menu
            theme="dark"
            mode="inline"
            defaultSelectedKeys={["2"]}
            selectedKeys={[selectedKey]}
            items={items}
            onClick={handleMenuClick}
          />
        </Sider>
        <Layout style={dashBoardClasses.containerHeader}>
          <Header style={getDashBoardHeaderStyle(colorBgContainer)}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={dashBoardClasses.buttomSpan}
            />

            <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
              <Space size="middle">
                <Badge count={5} size="small">
                  <MessageOutlined
                    style={{ fontSize: 16, cursor: "pointer" }}
                    onClick={() => setSelectedKey("7")}
                  />
                </Badge>

                <Badge count={3} size="small">
                  <InboxOutlined
                    style={{ fontSize: 16, cursor: "pointer" }}
                    onClick={() => setSelectedKey("8")}
                  />
                </Badge>

                <Badge count={2} size="small">
                  <FileDoneOutlined
                    style={{ fontSize: 16, cursor: "pointer" }}
                    onClick={() => setSelectedKey("3")}
                  />
                </Badge>
              </Space>

              <Button
                type="text"
                icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
                onClick={toggleDarkMode}
                style={dashBoardClasses.buttomSpan}
                title={
                  isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"
                }
              />
            </div>
          </Header>

          <Content style={{ flex: 1, margin: "10px 16px" }}>
            <div style={getDasBordContent(borderRadiusLG, isDarkMode)}>
              {renderContent()}
            </div>
          </Content>

          <Footer style={{ textAlign: "center" }}>
            Magnet Software House CO., LTD
          </Footer>
        </Layout>
      </Layout>

      <Modal
        title="Confirm Logout"
        open={isLogoutModalOpen}
        onOk={handleLogoutOk}
        onCancel={handleLogoutCancel}
        okText="OK"
        cancelText="Cancel"
        centered
      >
        <p>Are you sure you want to logout?</p>
      </Modal>
    </ConfigProvider>
  );
};

export default DashBord;
